/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-statements */
/* eslint-disable indent */
import { expectDomTypeError } from '@milkdown/exception';
import { toggleMark } from '@milkdown/prose/commands';
import type { Node as ProseNode } from '@milkdown/prose/model';
import { TextSelection, Plugin } from '@milkdown/prose/state';
import { $command, $markSchema, $nodeSchema } from '@milkdown/utils';
import type { Meta, MilkdownPlugin } from '@milkdown/ctx';
import { linkAttr } from '@milkdown/preset-commonmark';

export function withMeta<T extends MilkdownPlugin>(
  plugin: T,
  meta: Partial<Meta> & Pick<Meta, 'displayName'>
): T {
  Object.assign(plugin, {
    meta: {
      package: '@milkdown/preset-commonmark',
      ...meta,
    },
  });

  return plugin;
}

withMeta(linkAttr, {
  displayName: 'Attr<link>',
  group: 'Link',
});

/// Link mark schema.
export const linkSchema = $markSchema('link', (ctx) => {
  const schema = {
    attrs: {
      href: {},
      title: { default: null },
      target: { default: '_blank' },
    },
    inclusive: false,
    parseDOM: [
      {
        tag: 'a[href]',
        getAttrs: (dom) => {
          if (!(dom instanceof HTMLElement)) {
            throw expectDomTypeError(dom);
          }
          return {
            href: dom.getAttribute('href'),
            title: dom.getAttribute('title'),
            target: dom.getAttribute('target') || '_blank',
          };
        },
      },
    ],
    toDOM: (mark) => {
      const attrs = { ...ctx.get(linkAttr.key)(mark), ...mark.attrs };
      return ['a', attrs];
    },
    parseMarkdown: {
      match: (node) => node.type === 'link',
      runner: (state, node, markType) => {
        const url = node.url as string;
        const title = node.title as string;
        const target = (node.target as string) || '_blank';
        state.openMark(markType, { href: url, title, target });
        state.next(node.children);
        state.closeMark(markType);
      },
    },
    toMarkdown: {
      match: (mark) => mark.type.name === 'link',
      runner: (state, mark) => {
        state.withMark(mark, 'link', undefined, {
          title: mark.attrs.title,
          url: mark.attrs.href,
          target: mark.attrs.target,
        });
      },
    },
  };
  return schema;
});

withMeta(linkSchema.mark, {
  displayName: 'MarkSchema<link>',
  group: 'Link',
});

/// @internal
export interface UpdateLinkCommandPayload {
  href?: string;
  title?: string;
  target?: string;
}
/// A command to toggle the link mark.
/// You can pass the `href` and `title` to the link.
export const toggleLinkCommand = $command(
  'ToggleLink',
  (ctx) =>
    (payload: UpdateLinkCommandPayload = {}) =>
      toggleMark(linkSchema.type(ctx), payload)
);

withMeta(toggleLinkCommand, {
  displayName: 'Command<toggleLinkCommand>',
  group: 'Link',
});

/// A command to update the link mark.
/// You can pass the `href` and `title` to update the link.
export const updateLinkCommand = $command(
  'UpdateLink',
  (ctx) =>
    (payload: UpdateLinkCommandPayload = {}) =>
    (state, dispatch) => {
      if (!dispatch) {
        return false;
      }

      let node: ProseNode | undefined;
      let pos = -1;
      const { selection } = state;
      const { from, to } = selection;
      state.doc.nodesBetween(from, from === to ? to + 1 : to, (n, p) => {
        if (linkSchema.type(ctx).isInSet(n.marks)) {
          node = n;
          pos = p;
          return false;
        }

        return undefined;
      });

      if (!node) {
        return false;
      }

      const mark = node.marks.find(({ type }) => type === linkSchema.type(ctx));
      if (!mark) {
        return false;
      }

      const start = pos;
      const end = pos + node.nodeSize;
      const { tr } = state;
      const linkMark = linkSchema
        .type(ctx)
        .create({ ...mark.attrs, ...payload });
      if (!linkMark) {
        return false;
      }

      dispatch(
        tr
          .removeMark(start, end, mark)
          .addMark(start, end, linkMark)
          .setSelection(new TextSelection(tr.selection.$anchor))
          .scrollIntoView()
      );

      return true;
    }
);

withMeta(updateLinkCommand, {
  displayName: 'Command<updateLinkCommand>',
  group: 'Link',
});

// 正则表达式匹配链接+中文字符的模式
export const linkWithChineseRegex =
  /(https?:\/\/[^\s\u4e00-\u9fff\u3000-\u303f\uff00-\uffef，。！？；：""''（）【】《》]+)([\u4e00-\u9fff\u3000-\u303f\uff00-\uffef，。！？；：""''（）【】《》]+)/g;

/// 处理包含链接和中文字符的文本节点的 schema
export const linkWithChineseSchema = $nodeSchema('linkWithChinese', () => ({
  inline: true,
  group: 'inline',
  atom: true,
  marks: '',
  attrs: {
    content: { default: '' },
  },
  parseDOM: [
    {
      tag: 'span[data-type="linkWithChinese"]',
      getAttrs: (dom) => {
        if (!(dom instanceof HTMLElement)) {
          throw expectDomTypeError(dom);
        }
        return {
          content: dom.getAttribute('data-content') || '',
        };
      },
    },
  ],
  toDOM: (node) => {
    const content = node.attrs.content as string;
    const container = document.createElement('span');
    container.setAttribute('data-type', 'linkWithChinese');
    container.setAttribute('data-content', content);

    // 解析内容并创建链接和文本
    const match = linkWithChineseRegex.exec(content);
    if (match) {
      const [, cleanUrl, chineseChars] = match;

      // 创建链接元素
      const linkElement = document.createElement('a');
      linkElement.href = cleanUrl;
      linkElement.target = '_blank';
      linkElement.textContent = cleanUrl;

      // 添加链接和中文字符
      container.appendChild(linkElement);
      container.appendChild(document.createTextNode(chineseChars));
    } else {
      container.textContent = content;
    }

    return container;
  },
  parseMarkdown: {
    match: ({ type, value }) => {
      if (type === 'text' && typeof value === 'string') {
        // 重置正则表达式的 lastIndex
        linkWithChineseRegex.lastIndex = 0;
        return linkWithChineseRegex.test(value);
      }
      return false;
    },
    runner: (state, node, type) => {
      if (typeof node.value === 'string') {
        const { value } = node;
        // 重置正则表达式的 lastIndex
        linkWithChineseRegex.lastIndex = 0;
        let match;
        let lastIndex = 0;

        // 循环匹配所有的链接+中文组合
        // eslint-disable-next-line no-cond-assign
        while ((match = linkWithChineseRegex.exec(value)) !== null) {
          // 添加匹配之前的普通文本
          if (match.index > lastIndex) {
            state.addText(value.slice(lastIndex, match.index));
          }

          const [fullMatch] = match;

          // 添加 linkWithChinese 节点
          state.addNode(type, {
            content: fullMatch,
          });

          lastIndex = match.index + fullMatch.length;
        }

        // 添加最后一个匹配后的剩余文本
        if (lastIndex < value.length) {
          state.addText(value.slice(lastIndex));
        }
      }
    },
  },
  toMarkdown: {
    match: (node) => node.type.name === 'linkWithChinese',
    runner: (state, node) => {
      const content = node.attrs.content as string;
      state.addNode('text', undefined, undefined, {
        value: content,
      });
    },
  },
}));

withMeta(linkWithChineseSchema.node, {
  displayName: 'NodeSchema<linkWithChinese>',
  group: 'Link',
});
