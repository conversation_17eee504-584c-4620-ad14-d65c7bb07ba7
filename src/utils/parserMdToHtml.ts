/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable no-cond-assign */
/* eslint-disable no-unmodified-loop-condition */
/* eslint-disable max-depth */
/* eslint-disable max-lines */
/* eslint-disable max-statements */
import { Crepe, CrepeFeature } from '@/components/Crepe';
import { parserCtx, schemaCtx, serializerCtx } from '@milkdown/kit/core';
import { DOMSerializer, Node, Slice, Fragment } from '@milkdown/prose/model';
import { Ctx } from '@milkdown/kit/ctx';
import { FEATURES, featureConfigs } from '@/components/MdEditor/EditMd';
import {
  mentionSchema,
  mentionRegex,
} from '@/components/MdEditor/plugin-mention/MentionShecma';
import { linkSchema } from '@/components/MdEditor/plugin-link';

let ctx: null | Ctx = null;

export const initCtx = async () => {
  if (ctx) {
    return;
  }

  const crepeInstance = new Crepe({
    root: 'parserMd',
    defaultValue: '',
    features: FEATURES,
    // featureConfigs,
  });

  crepeInstance.editor.use(linkSchema).use(mentionSchema);

  await crepeInstance.create();
  ctx = crepeInstance?.editor.ctx;
};

export const parserMdToHtml = (
  mdContent: string,
  streamLabelProps?: {
    hasKnowledgeRetrieve?: boolean;
    hasNetworkSearch?: boolean;
  }
): string => {
  if (!mdContent) {
    return '';
  }
  try {
    // 将md字符转化为milkdown的doc
    // if (!ctx) {
    //   return parserMdToHtml(mdContent);
    // }
    if (!ctx) {
      return '没有初始化ctx';
    }

    // 预处理 mdContent，替换可能导致问题的 HTML 标签和转义 <think> 标签内容
    let processedMdContent = mdContent.replace(/<br\s*\/?>/gi, '\n');

    // 处理 <think> 标签：将其内部内容转换为Markdown引用语法
    // 兼容 <think> 未闭合的情况
    processedMdContent = processedMdContent.replace(
      /<think>([\s\S]*?)(<\/think>|$)/gi,
      (match, content, closingTag) => {
        return content
          .split('\n')
          .map((line: string) => `> ${line}`)
          .join('\n');
      }
    );

    const parser = ctx.get(parserCtx);
    if (!parser) {
      throw new Error('Parser context not initialized');
    }

    const doc = parser(processedMdContent);
    if (!doc) {
      return '';
    }

    // 获取 schema
    const schema = ctx.get(schemaCtx);
    if (!schema) {
      throw new Error('Schema context not initialized');
    }
    // 获取对应html片段
    const fragment = DOMSerializer.fromSchema(schema).serializeFragment(
      doc.content
    );
    const div = document.createElement('div');
    div.appendChild(fragment);
    const processedHtml = handleHtml(div.innerHTML, streamLabelProps);

    return processedHtml;
  } catch (error) {
    // console.error(error);

    return `<div style="white-space: pre-wrap; word-break: break-word; color: #1d1c1d; line-height: 22px; padding: 4px 0;">渲染md失败</div>`;
  }
};

export function extractTextWithListMarkers(doc: any): string {
  let result = '';

  const walk = (node: any, indent = '', ordered = false, index = 1) => {
    node.forEach((child: any) => {
      if (child.type.name === 'bullet_list') {
        walk(child, indent, false);
      } else if (child.type.name === 'ordered_list') {
        walk(child, indent, true, 1);
      } else if (child.type.name === 'list_item') {
        child.forEach((grandChild: any) => {
          if (grandChild.isTextblock) {
            const marker = ordered ? `${index}. ` : `●`;
            result += `${indent}${marker}${grandChild.textContent.trim()}\n`;
          } else {
            walk(grandChild, `${indent}  `, ordered, 1);
          }
        });
        if (ordered) {
          index++;
        }
      } else if (child.isTextblock) {
        result += `${indent}${child.textContent.trim()}\n`;
      } else {
        walk(child, indent, ordered, index);
      }
    });
  };

  walk(doc);
  return result.trim();
}

export const parserMdToText = (mdContent: string) => {
  if (!mdContent) {
    return '';
  }
  try {
    if (!ctx) {
      return '没有初始化ctx';
    }

    // 预处理 mdContent，替换可能导致问题的 HTML 标签并转义 <think> 标签内容
    let processedMdContent = mdContent.replace(/<br\s*\/?>/gi, '\n');

    // 处理 <think> 标签：将其内部内容转换为Markdown引用语法
    // 兼容 <think> 未闭合的情况
    processedMdContent = processedMdContent.replace(
      /<think>([\s\S]*?)(<\/think>|$)/gi,
      (match, content, closingTag) => {
        return content
          .split('\n')
          .map((line: string) => `> ${line}`)
          .join('\n');
      }
    );

    const parser = ctx.get(parserCtx);
    if (!parser) {
      throw new Error('Parser context not initialized');
    }

    const doc = parser(processedMdContent);

    const plainText = extractTextWithListMarkers(doc);
    return plainText;
  } catch (error) {
    // console.error(error);
    return '';
  }
};

const handleHtml = (html: string, streamLabelProps: any): string => {
  const container = document.createElement('div');
  container.innerHTML = html;
  const { hasKnowledgeRetrieve, hasNetworkSearch } = streamLabelProps || {};

  // 处理所有的hardbreak
  processHardBreaks(container);

  // 递归处理所有元素中的转义字符
  processEscapeCharacters(container);

  // 处理所有链接
  processLinks(container);

  // 处理所有的列表项
  processLists(container);

  // 处理所有的 p 标签中的 @提及
  processMentions(container);

  // 处理参考文献
  if (hasKnowledgeRetrieve) {
    processReferences(container, 'knowledge-retrieve');
  }
  if (hasNetworkSearch) {
    processReferences(container, 'network-search');
  }

  // 增加一个处理图片的函数，给所有的图片加上一个高度 200px
  processImages(container);

  // 处理所有代码块，使用CodeMirror渲染
  // await processCodeBlocks(container);

  return container.innerHTML;
};

// 处理图片，给所有图片添加样式和占位符逻辑
const processImages = (container: HTMLElement): void => {
  container.querySelectorAll('img').forEach((img) => {
    // 不对带有 code-language-icon 类的图片添加样式
    if (!img.classList.contains('code-language-icon')) {
      img.style.display = 'block';
      img.style.height = '100px';
      img.style.objectFit = 'contain';

      // 添加一个唯一标识符，用于在流式更新时识别图片
      const { src } = img;
      const alt = img.getAttribute('alt') || '';
      img.setAttribute('data-img-src', src);
      img.setAttribute('data-img-alt', alt);

      // 添加一个标记，表示这是一个需要处理的图片
      img.setAttribute('data-needs-processing', 'true');
    }
  });
};

// 处理硬换行
const processHardBreaks = (container: HTMLElement): void => {
  container.querySelectorAll('span[data-type="hardbreak"]').forEach((span) => {
    const br = document.createElement('br');
    span.parentNode?.replaceChild(br, span);
  });
};

// 检测字符串是否包含中文字符
const containsChinese = (text: string): boolean => {
  return /[\u4e00-\u9fff]/.test(text);
};

// 提取字符串中的中文字符，保持原有顺序和空格
const extractChinese = (text: string): string => {
  return text.replace(/[^\u4e00-\u9fff\s]/g, '').trim();
};

// 移除字符串中的中文字符
const removeChinese = (text: string): string => {
  return text.replace(/[\u4e00-\u9fff]/g, '').trim();
};

// 处理链接，确保所有链接都有 target="_blank" 属性
const processLinks = (container: HTMLElement): void => {
  container.querySelectorAll('a').forEach((a) => {
    // 设置链接在新窗口打开

    const linkText = a.textContent || '';
    const href = a.getAttribute('href') || '';

    // 检查链接文本是否包含中文字符
    if (containsChinese(linkText)) {
      // 提取中文部分
      const chinesePart = extractChinese(linkText);
      // 移除中文部分，保留非中文部分
      const nonChinesePart = removeChinese(linkText);

      // 处理 href 属性，移除其中的中文字符
      const cleanHref = removeChinese(href);

      // 如果有中文部分，创建新的 span 标签
      if (chinesePart) {
        // 创建包含中文的 span 标签
        const chineseSpan = document.createElement('span');
        chineseSpan.textContent = chinesePart;

        // 在 a 标签前插入 span 标签
        a.parentNode?.insertBefore(chineseSpan, a);

        // 更新 a 标签的内容和 href
        a.textContent = nonChinesePart;
        if (cleanHref !== href) {
          a.setAttribute('href', cleanHref);
        }

        // 如果移除中文后链接文本为空，则隐藏链接
        if (!nonChinesePart) {
          a.style.display = 'none';
        }
      }
    }
  });
};

// 处理列表项
const processLists = (container: HTMLElement): void => {
  container.querySelectorAll('li').forEach((li) => {
    const dataLabel = li.getAttribute('data-label');
    const dataListType = li.getAttribute('data-list-type');
    const indentLevel = getIndentLevel(li);

    // 设置 li 的自定义属性，用于 CSS 伪元素显示
    li.setAttribute('data-content', dataLabel || '');
    li.classList.add(
      dataListType === 'ordered' ? 'ordered-item' : 'unordered-item'
    );
    li.style.paddingLeft = `${28}px`; // 缩进 + 序号宽度
    // li.style.paddingLeft = `${indentLevel * 24 + 28}px`; // 缩进 + 序号宽度

    // 处理换行情况
    if (li.querySelectorAll('span[data-type="hardbreak"]').length > 0) {
      const paragraphs = Array.from(li.children);
      paragraphs.forEach((p, index) => {
        if (index > 0 && p instanceof HTMLElement) {
          p.style.marginLeft = '0'; // 移除之前的额外缩进
        }
      });
    }
  });
};

// 处理@提及
const processMentions = (container: HTMLElement): void => {
  container.querySelectorAll('p').forEach((p) => {
    const mentionRegex = /@\{([^}]+)\}/g;
    let content = p.innerHTML;
    content = content.replace(mentionRegex, (_, name) => {
      return `<span data-type="mention" class="milkdown-mention" contenteditable="false">@${name.trim()}</span>`;
    });
    p.innerHTML = content;
  });
};

function getIndentLevel(li: Element): number {
  let indentLevel = 0;
  let parent = li.parentElement;
  while (parent) {
    if (
      parent.tagName.toLowerCase() === 'ul' ||
      parent.tagName.toLowerCase() === 'ol'
    ) {
      const parentLi = parent.parentElement;
      if (parentLi && parentLi.tagName.toLowerCase() === 'li') {
        indentLevel++;
      }
    }
    parent = parent.parentElement;
  }
  return indentLevel;
}

// 处理转义字符
export const processEscapeCharacters = (content: string | Element): string => {
  if (typeof content === 'string') {
    return content.replace(/\\([\\`*{}[\]()#+\-.!_>])/g, '$1');
  }

  if (content.childNodes.length === 0) {
    return content.textContent || '';
  }

  for (const node of Array.from(content.childNodes)) {
    if (node.nodeType === 3 /* Node.TEXT_NODE */ && node.textContent) {
      node.textContent = node.textContent.replace(
        /\\([\\`*{}[\]()#+\-.!_>])/g,
        '$1'
      );
    } else if (node.nodeType === 1 /* Node.ELEMENT_NODE */) {
      processEscapeCharacters(node as Element);
    }
  }

  return content.innerHTML;
};

const replaceMentionText = (node: Node) => {
  // 处理 mention 节点
  if (node.type.name === 'mention') {
    const attrs = { ...node.attrs };

    // 替换 name 属性中的 mention 格式为 @显示文本
    if (typeof attrs.name === 'string') {
      attrs.name = attrs.name.replace(mentionRegex, '@$1');
    }

    // 保持 mention 节点的子节点不变（如果有的话）
    return node.copy(node.content);
  }

  // 如果是叶子节点或没有子节点，直接返回
  if (node.isLeaf || node.childCount === 0) {
    return node;
  }

  // 递归处理子节点
  const newChildren: Node[] = [];
  for (let i = 0; i < node.childCount; i++) {
    const child = node.child(i);
    newChildren.push(replaceMentionText(child));
  }

  // 使用替换后的子节点创建新内容
  const fragment = Fragment.from(newChildren);
  return node.copy(fragment);
};
export const getMarkdownFromJson = (json: string) => {
  if (!ctx) {
    return '没有初始化ctx';
  }
  try {
    const serializer = ctx.get(serializerCtx);

    const schema = ctx.get(schemaCtx);

    const doc = Node.fromJSON(schema, JSON.parse(json));

    const processedDoc = replaceMentionText(doc);
    const markdown = serializer(processedDoc);

    return markdown;
  } catch (e) {
    return json;
  }
};

// 处理参考文献
const processReferences = (
  container: HTMLElement,
  labelName = 'reference-button'
): void => {
  // 添加参考文献按钮样式
  const style = document.createElement('style');
  style.textContent = `
    .${labelName} {
      border: none;
      box-shadow: none;
      color: var(--primary-text-color-1);
      background: color-mix(in srgb, var(--tab-actived-background-color), transparent 70%);
      transition: background 0.2s;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 18px;
      height: 18px;
      border-radius: 50%;
      cursor: pointer;
      font-size: 12px;
      margin: 0 2px;
      padding: 0;
    }
    .${labelName}:hover {
      background: var(--tab-actived-background-color);
    }
 
  `;
  container.insertBefore(style, container.firstChild);

  // 忽略display:none的pre标签内的内容
  const hiddenElements = container.querySelectorAll(
    'pre[style*="display: none"], pre[style*="display:none"]'
  );
  const hiddenNodes = new Set<globalThis.Node>();

  hiddenElements.forEach((elem) => {
    // 收集所有隐藏元素内的节点
    const nodeIterator = document.createNodeIterator(elem, NodeFilter.SHOW_ALL);
    let node;
    while ((node = nodeIterator.nextNode())) {
      hiddenNodes.add(node);
    }
    // 添加元素本身
    hiddenNodes.add(elem);
  });

  // 查找并处理所有文本节点中的参考文献标记 [数字]
  const walker = document.createTreeWalker(
    container,
    4 /* NodeFilter.SHOW_TEXT */,
    {
      acceptNode(node) {
        // 跳过隐藏元素内的节点
        if (
          hiddenNodes.has(node) ||
          (node.parentNode &&
            hiddenNodes.has(node.parentNode as globalThis.Node))
        ) {
          return NodeFilter.FILTER_REJECT;
        }
        return NodeFilter.FILTER_ACCEPT;
      },
    } as NodeFilter
  );

  const nodesToProcess: { node: Text; matches: RegExpMatchArray[] }[] = [];

  let currentNode: Text | null;
  while ((currentNode = walker.nextNode() as Text)) {
    const text = currentNode.textContent || '';
    const matches = Array.from(text.matchAll(/\[(\d+)\]/g));

    if (matches.length > 0) {
      nodesToProcess.push({ node: currentNode, matches });
    }
  }

  // 从后向前处理，以避免处理过程中改变节点位置影响其他匹配
  for (let i = nodesToProcess.length - 1; i >= 0; i--) {
    const { node, matches } = nodesToProcess[i];
    let text = node.textContent || '';
    let lastIndex = text.length;

    // 从后向前替换
    for (let j = matches.length - 1; j >= 0; j--) {
      const match = matches[j];
      const matchText = match[0];
      const referenceNumber = match[1];
      const startIndex = match.index!;
      const endIndex = startIndex + matchText.length;

      // 创建参考文献按钮
      const button = document.createElement('button');
      button.className = labelName;
      button.textContent = referenceNumber;
      button.setAttribute('data-reference', referenceNumber);
      button.setAttribute('type', 'button');

      // 分割文本节点并插入按钮
      const afterText = text.substring(endIndex, lastIndex);
      if (afterText) {
        const afterNode = document.createTextNode(afterText);
        node.parentNode?.insertBefore(afterNode, node.nextSibling);
      }

      node.parentNode?.insertBefore(button, node.nextSibling);

      // 更新文本和lastIndex
      text = text.substring(0, startIndex);
      lastIndex = startIndex;
    }

    // 更新原始节点的文本
    node.textContent = text;
  }
};
