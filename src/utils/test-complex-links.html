<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试复杂链接处理</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .test-case {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-case h3 {
            margin-top: 0;
            color: #333;
        }
        .before, .after {
            margin: 10px 0;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 3px;
            word-break: break-all;
        }
        .before {
            background: #fff3cd;
        }
        .after {
            background: #d4edda;
        }
        span {
            background: #e7f3ff;
            padding: 2px 4px;
            border-radius: 2px;
            margin: 0 2px;
        }
        a {
            color: #007bff;
            text-decoration: underline;
            margin: 0 2px;
        }
        a[style*="display: none"] {
            background: #ffcccc;
        }
    </style>
</head>
<body>
    <h1>复杂链接处理测试</h1>
    
    <div class="test-case">
        <h3>测试用例 1: 复杂混合文本（原始场景）</h3>
        <div class="before">
            <strong>处理前:</strong>
            <div id="test1-before">
                我没办法直接登录平台去查找和复制报告URL呢。你可以按照上述步骤在devInsight平台（<a href="http://eip.htsc.com.cn/devinsight">http://eip.htsc.com.cn/devinsight</a>）（40），URL，。<a href="http://eipdev.htsc.com.cn/linkflow/chat">http://eipdev.htsc.com.cn/linkflow/chat</a>上找到符合要求分析指标在个以下的洞察报告然后把报告的提供给我我就能调用报告解读工具为你解读报告啦你好你好你好
            </div>
        </div>
        <div class="after">
            <strong>处理后:</strong>
            <div id="test1-after"></div>
        </div>
    </div>

    <div class="test-case">
        <h3>测试用例 2: 多个链接，部分包含中文</h3>
        <div class="before">
            <strong>处理前:</strong>
            <div id="test2-before">
                访问 <a href="https://example.com/中文路径">https://example.com/中文路径</a> 或者 <a href="https://google.com">https://google.com</a> 查看更多信息。还有 <a href="https://test.com/测试页面">https://test.com/测试页面</a> 可以参考。
            </div>
        </div>
        <div class="after">
            <strong>处理后:</strong>
            <div id="test2-after"></div>
        </div>
    </div>

    <div class="test-case">
        <h3>测试用例 3: 链接文本与href不匹配</h3>
        <div class="before">
            <strong>处理前:</strong>
            <div id="test3-before">
                点击 <a href="https://example.com/中文路径">这里访问中文页面</a> 或者 <a href="https://google.com">Google搜索</a> 查看。
            </div>
        </div>
        <div class="after">
            <strong>处理后:</strong>
            <div id="test3-after"></div>
        </div>
    </div>

    <div class="test-case">
        <h3>测试用例 4: 纯中文链接</h3>
        <div class="before">
            <strong>处理前:</strong>
            <div id="test4-before">
                访问 <a href="中文网站.com">中文网站.com</a> 了解更多。
            </div>
        </div>
        <div class="after">
            <strong>处理后:</strong>
            <div id="test4-after"></div>
        </div>
    </div>

    <script>
        // 复制优化后的 processLinks 相关函数
        const containsChinese = (text) => {
            return /[\u4e00-\u9fff]/.test(text);
        };

        const extractChinese = (text) => {
            return text.replace(/[^\u4e00-\u9fff\s]/g, '').trim();
        };

        const removeChinese = (text) => {
            return text.replace(/[\u4e00-\u9fff]/g, '').trim();
        };

        const processLinks = (container) => {
            // 使用 Array.from 创建静态副本，避免在处理过程中DOM变化影响遍历
            const links = Array.from(container.querySelectorAll('a'));
            
            links.forEach((a) => {
                try {
                    // 设置链接在新窗口打开
                    if (!a.getAttribute('target')) {
                        a.setAttribute('target', '_blank');
                        a.setAttribute('rel', 'noopener noreferrer');
                    }

                    const linkText = a.textContent?.trim() || '';
                    const href = a.getAttribute('href')?.trim() || '';

                    // 仅当链接文本与 href 属性值完全相等且包含中文字符时，才执行中文字符分离处理
                    if (linkText === href && containsChinese(linkText)) {
                        // 提取中文部分
                        const chinesePart = extractChinese(linkText);
                        // 移除中文部分，保留非中文部分
                        const nonChinesePart = removeChinese(linkText);

                        // 处理 href 属性，移除其中的中文字符
                        const cleanHref = removeChinese(href);

                        // 确保有中文部分且父节点存在
                        if (chinesePart && a.parentNode) {
                            // 创建包含中文的 span 标签
                            const chineseSpan = document.createElement('span');
                            chineseSpan.textContent = chinesePart;

                            // 安全地在 a 标签后插入 span 标签
                            if (a.nextSibling) {
                                a.parentNode.insertBefore(chineseSpan, a.nextSibling);
                            } else {
                                a.parentNode.appendChild(chineseSpan);
                            }

                            // 更新 a 标签的内容和 href
                            a.textContent = nonChinesePart;
                            if (cleanHref !== href && cleanHref) {
                                a.setAttribute('href', cleanHref);
                            }

                            // 如果移除中文后链接文本为空，则隐藏链接但保留结构
                            if (!nonChinesePart) {
                                a.style.display = 'none';
                            }
                        }
                    }
                } catch (error) {
                    // 静默处理错误，确保其他链接的处理不受影响
                    console.warn('处理链接时出现错误:', error, a);
                }
            });
        };

        // 运行测试
        function runTests() {
            // 测试用例 1
            const test1Before = document.getElementById('test1-before');
            const test1After = document.getElementById('test1-after');
            test1After.innerHTML = test1Before.innerHTML;
            processLinks(test1After);

            // 测试用例 2
            const test2Before = document.getElementById('test2-before');
            const test2After = document.getElementById('test2-after');
            test2After.innerHTML = test2Before.innerHTML;
            processLinks(test2After);

            // 测试用例 3
            const test3Before = document.getElementById('test3-before');
            const test3After = document.getElementById('test3-after');
            test3After.innerHTML = test3Before.innerHTML;
            processLinks(test3After);

            // 测试用例 4
            const test4Before = document.getElementById('test4-before');
            const test4After = document.getElementById('test4-after');
            test4After.innerHTML = test4Before.innerHTML;
            processLinks(test4After);
        }

        // 页面加载完成后运行测试
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>
