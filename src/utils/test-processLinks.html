<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试 processLinks 函数</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }

        .test-case {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .test-case h3 {
            margin-top: 0;
            color: #333;
        }

        .before,
        .after {
            margin: 10px 0;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 3px;
        }

        .before {
            background: #fff3cd;
        }

        .after {
            background: #d4edda;
        }

        span {
            background: #e7f3ff;
            padding: 2px 4px;
            border-radius: 2px;
            margin-right: 4px;
        }

        a {
            color: #007bff;
            text-decoration: underline;
        }
    </style>
</head>

<body>
    <h1>processLinks 函数测试</h1>

    <div class="test-case">
        <h3>测试用例 1: 包含中文的链接</h3>
        <div class="before">
            <strong>处理前:</strong>
            <div id="test1-before">
                <a href="https://example.com/中文路径">这是中文链接text</a>
            </div>
        </div>
        <div class="after">
            <strong>处理后:</strong>
            <div id="test1-after"></div>
        </div>
    </div>

    <div class="test-case">
        <h3>测试用例 2: 纯中文链接</h3>
        <div class="before">
            <strong>处理前:</strong>
            <div id="test2-before">
                <a href="https://example.com/中文">中文链接</a>
            </div>
        </div>
        <div class="after">
            <strong>处理后:</strong>
            <div id="test2-after"></div>
        </div>
    </div>

    <div class="test-case">
        <h3>测试用例 3: 纯英文链接</h3>
        <div class="before">
            <strong>处理前:</strong>
            <div id="test3-before">
                <a href="https://example.com/english">English Link</a>
            </div>
        </div>
        <div class="after">
            <strong>处理后:</strong>
            <div id="test3-after"></div>
        </div>
    </div>

    <div class="test-case">
        <h3>测试用例 4: 混合内容</h3>
        <div class="before">
            <strong>处理前:</strong>
            <div id="test4-before">
                这里有一个 <a href="https://example.com/测试">测试链接Test</a> 和另一个 <a href="https://google.com">Google</a>
            </div>
        </div>
        <div class="after">
            <strong>处理后:</strong>
            <div id="test4-after"></div>
        </div>
    </div>

    <div class="test-case">
        <h3>测试用例 5: 中文和空格</h3>
        <div class="before">
            <strong>处理前:</strong>
            <div id="test5-before">
                <a href="https://example.com/中文 路径">中文 链接 Test Link</a>
            </div>
        </div>
        <div class="after">
            <strong>处理后:</strong>
            <div id="test5-after"></div>
        </div>
    </div>

    <div class="test-case">
        <h3>测试用例 6: 只有中文的链接</h3>
        <div class="before">
            <strong>处理前:</strong>
            <div id="test6-before">
                <a href="https://example.com/中文路径">中文链接</a>
            </div>
        </div>
        <div class="after">
            <strong>处理后:</strong>
            <div id="test6-after"></div>
        </div>
    </div>

    <script>
        // 复制 processLinks 相关的函数
        const containsChinese = (text) => {
            return /[\u4e00-\u9fff]/.test(text);
        };

        const extractChinese = (text) => {
            return text.replace(/[^\u4e00-\u9fff\s]/g, '').trim();
        };

        const removeChinese = (text) => {
            return text.replace(/[\u4e00-\u9fff]/g, '').trim();
        };

        const processLinks = (container) => {
            container.querySelectorAll('a').forEach((a) => {
                // 设置链接在新窗口打开
                if (!a.getAttribute('target')) {
                    a.setAttribute('target', '_blank');
                    a.setAttribute('rel', 'noopener noreferrer');
                }

                const linkText = a.textContent || '';
                const href = a.getAttribute('href') || '';

                // 检查链接文本是否包含中文字符
                if (containsChinese(linkText)) {
                    // 提取中文部分
                    const chinesePart = extractChinese(linkText);
                    // 移除中文部分，保留非中文部分
                    const nonChinesePart = removeChinese(linkText);

                    // 处理 href 属性，移除其中的中文字符
                    const cleanHref = removeChinese(href);

                    // 如果有中文部分，创建新的 span 标签
                    if (chinesePart) {
                        // 创建包含中文的 span 标签
                        const chineseSpan = document.createElement('span');
                        chineseSpan.textContent = chinesePart;

                        // 在 a 标签前插入 span 标签
                        a.parentNode?.insertBefore(chineseSpan, a);

                        // 更新 a 标签的内容和 href
                        a.textContent = nonChinesePart;
                        if (cleanHref !== href) {
                            a.setAttribute('href', cleanHref);
                        }

                        // 如果移除中文后链接文本为空，则隐藏链接
                        if (!nonChinesePart) {
                            a.style.display = 'none';
                        }
                    }
                }
            });
        };

        // 运行测试
        function runTests() {
            // 测试用例 1
            const test1Before = document.getElementById('test1-before');
            const test1After = document.getElementById('test1-after');
            test1After.innerHTML = test1Before.innerHTML;
            processLinks(test1After);

            // 测试用例 2
            const test2Before = document.getElementById('test2-before');
            const test2After = document.getElementById('test2-after');
            test2After.innerHTML = test2Before.innerHTML;
            processLinks(test2After);

            // 测试用例 3
            const test3Before = document.getElementById('test3-before');
            const test3After = document.getElementById('test3-after');
            test3After.innerHTML = test3Before.innerHTML;
            processLinks(test3After);

            // 测试用例 4
            const test4Before = document.getElementById('test4-before');
            const test4After = document.getElementById('test4-after');
            test4After.innerHTML = test4Before.innerHTML;
            processLinks(test4After);

            // 测试用例 5
            const test5Before = document.getElementById('test5-before');
            const test5After = document.getElementById('test5-after');
            test5After.innerHTML = test5Before.innerHTML;
            processLinks(test5After);

            // 测试用例 6
            const test6Before = document.getElementById('test6-before');
            const test6After = document.getElementById('test6-after');
            test6After.innerHTML = test6Before.innerHTML;
            processLinks(test6After);
        }

        // 页面加载完成后运行测试
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>

</html>